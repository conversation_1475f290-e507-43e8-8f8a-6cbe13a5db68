{"name": "macos-voice-interpretation-app", "version": "1.0.0", "description": "MacOS application for real-time voice interpretation during meetings", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"@radix-ui/react-accordion": "1.2.3", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.3", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.3", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-menubar": "^1.1.2", "@radix-ui/react-navigation-menu": "^1.2.2", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "2.1.6", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "1.1.2", "@radix-ui/react-switch": "1.1.3", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.5", "class-variance-authority": "0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.3", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^11.16.1", "input-otp": "^1.4.1", "lucide-react": "0.487.0", "react": "^18.2.0", "react-day-picker": "^9.4.2", "react-dom": "^18.2.0", "react-resizable-panels": "2.1.7", "recharts": "^2.13.3", "sonner": "^1.7.1", "tailwind-merge": "^2.5.0", "vaul": "^1.1.1"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.10", "@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "@typescript-eslint/eslint-plugin": "^8.17.0", "@typescript-eslint/parser": "^8.17.0", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "postcss": "^8.5.6", "tailwindcss": "^4.1.10", "typescript": "^5.7.2", "vite": "^6.0.5"}, "keywords": ["voice-interpretation", "real-time-translation", "meeting-assistant", "macos-app", "react", "typescript", "tailwindcss"], "author": "Voice Interpretation Team", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}