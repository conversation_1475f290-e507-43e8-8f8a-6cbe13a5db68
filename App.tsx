import { useState, useEffect } from "react";
import { TranslationPanel } from "./components/TranslationPanel";
import { SummaryPanel } from "./components/SummaryPanel";
import { ControlPanel } from "./components/ControlPanel";
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from "./components/ui/resizable";

interface Translation {
  id: string;
  timestamp: string;
  originalText: string;
  translatedText: string;
  originalLang: string;
  targetLang: string;
  confidence: 'high' | 'medium' | 'low';
}

interface SummaryItem {
  id: string;
  timeRange: string;
  summary: string;
  keyPoints: string[];
}

export default function App() {
  // 상태 관리
  const [isRecording, setIsRecording] = useState(false);
  const [sourceLang, setSourceLang] = useState('auto');
  const [targetLang, setTargetLang] = useState('ko');
  const [isOnlineMode, setIsOnlineMode] = useState(false);
  const [isSummaryPanelVisible, setIsSummaryPanelVisible] = useState(true);
  const [isQuestionHelperVisible, setIsQuestionHelperVisible] = useState(true);
  const [translations, setTranslations] = useState<Translation[]>([]);
  const [currentTopic, setCurrentTopic] = useState("");
  const [summaries, setSummaries] = useState<SummaryItem[]>([]);
  const [suggestedQuestions, setSuggestedQuestions] = useState<string[]>([]);

  // 모의 데이터 (실제로는 실시간 음성 인식 결과) - 문단 단위로 변경
  useEffect(() => {
    const mockTranslations: Translation[] = [
      {
        id: '1',
        timestamp: '10:42:15',
        originalText: 'Let me start by explaining our current API performance challenges. We have been experiencing some latency issues, particularly during peak usage hours. The average response time has increased from 50 milliseconds to approximately 150 milliseconds over the past month. This degradation is primarily affecting our user authentication endpoints and data retrieval services. We need to identify the root cause and implement solutions to bring performance back to acceptable levels.',
        translatedText: '현재 API 성능 문제에 대해 설명드리겠습니다. 특히 피크 사용 시간대에 지연 문제를 겪고 있습니다. 평균 응답 시간이 지난 달 50밀리초에서 약 150밀리초로 증가했습니다. 이러한 성능 저하는 주로 사용자 인증 엔드포인트와 데이터 검색 서비스에 영향을 미치고 있습니다. 근본 원인을 파악하고 성능을 허용 가능한 수준으로 되돌리기 위한 솔루션을 구현해야 합니다.',
        originalLang: 'EN',
        targetLang: 'KO',
        confidence: 'high'
      },
      {
        id: '2',
        timestamp: '10:43:45',
        originalText: '네, 말씀하신 성능 이슈에 대해서는 저희 팀에서도 모니터링하고 있었습니다. 데이터베이스 쿼리 최적화와 캐싱 전략 개선이 필요할 것 같습니다. 특히 사용자 인증 관련해서는 Redis 캐시 서버의 메모리 사용량이 90%를 넘어서고 있어서 이 부분을 우선적으로 해결해야 할 것 같습니다. 또한 데이터베이스 연결 풀 설정도 재검토가 필요합니다.',
        translatedText: 'Yes, our team has also been monitoring the performance issues you mentioned. It seems we need database query optimization and improved caching strategies. Particularly for user authentication, the Redis cache server memory usage is exceeding 90%, so we should prioritize resolving this issue. Additionally, we need to review the database connection pool settings.',
        originalLang: 'KO',
        targetLang: 'EN',
        confidence: 'high'
      },
      {
        id: '3',
        timestamp: '10:45:20',
        originalText: 'That\'s a great point about the Redis cache. I think we should also consider implementing a distributed caching strategy to handle the increased load. Additionally, we might want to explore database sharding options for our user data tables. The current single-instance setup is becoming a bottleneck. We should also implement proper monitoring and alerting systems to catch these issues earlier in the future.',
        translatedText: 'Redis 캐시에 대한 좋은 지적입니다. 증가한 부하를 처리하기 위해 분산 캐싱 전략 구현도 고려해야 한다고 생각합니다. 또한 사용자 데이터 테이블에 대한 데이터베이스 샤딩 옵션도 검토해볼 필요가 있습니다. 현재의 단일 인스턴스 설정이 병목 현상을 일으키고 있습니다. 향후 이러한 문제를 조기에 발견할 수 있도록 적절한 모니터링 및 알림 시스템도 구현해야 합니다.',
        originalLang: 'EN',
        targetLang: 'KO',
        confidence: 'medium'
      }
    ];

    const mockSummaries: SummaryItem[] = [
      {
        id: '1',
        timeRange: '10:35-10:40',
        summary: '프로젝트 일정 및 주요 마일스톤 확인',
        keyPoints: ['12월 말 베타 출시', 'QA 프로세스 강화']
      },
      {
        id: '2',
        timeRange: '10:40-10:45',
        summary: 'API 성능 문제 및 해결 방안 논의',
        keyPoints: ['평균 응답시간 150ms로 증가', 'Redis 캐시 메모리 90% 초과', 'DB 쿼리 최적화 필요']
      },
      {
        id: '3',
        timeRange: '10:45-10:50',
        summary: '분산 캐싱 및 데이터베이스 샤딩 방안 검토',
        keyPoints: ['분산 캐싱 전략 수립', '데이터베이스 샤딩 고려', '모니터링 시스템 구축']
      }
    ];

    const mockSuggestedQuestions = [
      "분산 캐싱 구현 일정은 어떻게 되나요?",
      "데이터베이스 샤딩 비용은 얼마나 들까요?",
      "현재 시스템 모니터링 도구는 무엇을 사용하고 있나요?"
    ];

    setTranslations(mockTranslations);
    setSummaries(mockSummaries);
    setCurrentTopic("API 성능 최적화 및 인프라 개선 방안");
    setSuggestedQuestions(mockSuggestedQuestions);
  }, []);

  const handleRecordingToggle = () => {
    setIsRecording(!isRecording);
    if (!isRecording) {
      console.log("음성 인식 시작");
      // 실제로는 음성 인식 서비스 시작
    } else {
      console.log("음성 인식 중지");
      // 실제로는 음성 인식 서비스 중지
    }
  };

  const handleSummaryPanelToggle = () => {
    setIsSummaryPanelVisible(!isSummaryPanelVisible);
  };

  const handleQuestionHelperToggle = () => {
    setIsQuestionHelperVisible(!isQuestionHelperVisible);
  };

  return (
    <div className="h-screen flex bg-gray-50 overflow-hidden">
      {isSummaryPanelVisible ? (
        /* Resizable layout when summary panel is visible */
        <ResizablePanelGroup direction="horizontal" className="h-full w-full">
          {/* 좌측: 실시간 통역 패널 + 제어 패널 */}
          <ResizablePanel defaultSize={70} minSize={50} maxSize={85}>
            <div className="flex-1 flex flex-col min-h-0 overflow-hidden h-full">
              {/* 실시간 통역 패널 */}
              <div className="flex-1 min-h-0 overflow-hidden">
                <TranslationPanel 
                  translations={translations}
                  isSummaryPanelVisible={isSummaryPanelVisible}
                  onSummaryPanelToggle={handleSummaryPanelToggle}
                  isQuestionHelperVisible={isQuestionHelperVisible}
                  onQuestionHelperToggle={handleQuestionHelperToggle}
                />
              </div>
              
              {/* 제어 패널 */}
              <div className="shrink-0 overflow-visible">
                <ControlPanel
                  isRecording={isRecording}
                  onRecordingToggle={handleRecordingToggle}
                  sourceLang={sourceLang}
                  targetLang={targetLang}
                  onSourceLangChange={setSourceLang}
                  onTargetLangChange={setTargetLang}
                  isOnlineMode={isOnlineMode}
                  onModeToggle={() => setIsOnlineMode(!isOnlineMode)}
                  suggestedQuestions={suggestedQuestions}
                  isQuestionHelperVisible={isQuestionHelperVisible}
                  onQuestionHelperToggle={handleQuestionHelperToggle}
                />
              </div>
            </div>
          </ResizablePanel>

          {/* Resizable handle */}
          <ResizableHandle 
            withHandle 
            className="bg-gray-200 hover:bg-gray-300 transition-colors duration-200"
          />

          {/* 우측: 요약 및 주제 패널 */}
          <ResizablePanel defaultSize={30} minSize={15} maxSize={50}>
            <div className="h-full overflow-hidden">
              <SummaryPanel 
                currentTopic={currentTopic}
                summaries={summaries}
              />
            </div>
          </ResizablePanel>
        </ResizablePanelGroup>
      ) : (
        /* Full width layout when summary panel is hidden */
        <div className="flex-1 flex flex-col min-h-0 overflow-hidden">
          {/* 실시간 통역 패널 */}
          <div className="flex-1 min-h-0 overflow-hidden">
            <TranslationPanel 
              translations={translations}
              isSummaryPanelVisible={isSummaryPanelVisible}
              onSummaryPanelToggle={handleSummaryPanelToggle}
              isQuestionHelperVisible={isQuestionHelperVisible}
              onQuestionHelperToggle={handleQuestionHelperToggle}
            />
          </div>
          
          {/* 제어 패anel */}
          <div className="shrink-0 overflow-visible">
            <ControlPanel
              isRecording={isRecording}
              onRecordingToggle={handleRecordingToggle}
              sourceLang={sourceLang}
              targetLang={targetLang}
              onSourceLangChange={setSourceLang}
              onTargetLangChange={setTargetLang}
              isOnlineMode={isOnlineMode}
              onModeToggle={() => setIsOnlineMode(!isOnlineMode)}
              suggestedQuestions={suggestedQuestions}
              isQuestionHelperVisible={isQuestionHelperVisible}
              onQuestionHelperToggle={handleQuestionHelperToggle}
            />
          </div>
        </div>
      )}
    </div>
  );
}