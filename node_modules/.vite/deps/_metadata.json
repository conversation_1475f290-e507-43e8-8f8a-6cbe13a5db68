{"hash": "67d2dc42", "configHash": "75b7c703", "lockfileHash": "02a2eeab", "browserHash": "6c0630b1", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "9e6d1ff8", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "8e9ed2a9", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "f2e015aa", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "d48311d7", "needsInterop": true}, "@radix-ui/react-label": {"src": "../../@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "dce4fe30", "needsInterop": false}, "@radix-ui/react-scroll-area": {"src": "../../@radix-ui/react-scroll-area/dist/index.mjs", "file": "@radix-ui_react-scroll-area.js", "fileHash": "6995e55e", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "fc92d12c", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "fd168ba0", "needsInterop": false}, "@radix-ui/react-switch": {"src": "../../@radix-ui/react-switch/dist/index.mjs", "file": "@radix-ui_react-switch.js", "fileHash": "f4e69c73", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "6fddbb72", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "4d3f1581", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "07d44075", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "051c646e", "needsInterop": true}, "react-resizable-panels": {"src": "../../react-resizable-panels/dist/react-resizable-panels.browser.development.esm.js", "file": "react-resizable-panels.js", "fileHash": "a27b0de2", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "f483e4a4", "needsInterop": false}}, "chunks": {"chunk-LFJJZAZU": {"file": "chunk-LFJJZAZU.js"}, "chunk-HDHGXYFG": {"file": "chunk-HDHGXYFG.js"}, "chunk-NXESFFTV": {"file": "chunk-NXESFFTV.js"}, "chunk-6PXSGDAH": {"file": "chunk-6PXSGDAH.js"}, "chunk-DRWLMN53": {"file": "chunk-DRWLMN53.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-G3PMV62Z": {"file": "chunk-G3PMV62Z.js"}}}