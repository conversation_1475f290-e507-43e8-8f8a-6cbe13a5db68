import { Clock, Volume2 } from "lucide-react";

interface TranslationBlockProps {
  id: string;
  timestamp: string;
  originalText: string;
  translatedText: string;
  originalLang: string;
  targetLang: string;
  confidence: 'high' | 'medium' | 'low';
  onPlay?: (id: string) => void;
}

export function TranslationBlock({
  id,
  timestamp,
  originalText,
  translatedText,
  originalLang,
  targetLang,
  confidence,
  onPlay
}: TranslationBlockProps) {
  const confidenceColors = {
    high: 'border-l-green-500 bg-green-50',
    medium: 'border-l-yellow-500 bg-yellow-50',
    low: 'border-l-orange-500 bg-orange-50'
  };

  const confidenceText = {
    high: '높음',
    medium: '보통',
    low: '낮음'
  };

  return (
    <div className={`p-4 mb-4 border-l-4 rounded-lg ${confidenceColors[confidence]} hover:shadow-sm transition-shadow cursor-pointer`}
         onClick={() => onPlay?.(id)}>
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <Clock size={14} className="text-muted-foreground" />
          <span className="text-xs text-muted-foreground">{timestamp}</span>
        </div>
        <div className="flex items-center gap-3">
          <span className="text-xs text-muted-foreground">신뢰도: {confidenceText[confidence]}</span>
          <Volume2 size={14} className="text-muted-foreground hover:text-primary transition-colors" />
        </div>
      </div>
      
      <div className="space-y-2">
        <div>
          <div className="flex items-center gap-2 mb-1.5">
            <span className="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded uppercase">{originalLang}</span>
            <span className="text-xs text-muted-foreground">원문</span>
          </div>
          <p className="text-sm text-gray-700 leading-relaxed mb-2">{originalText}</p>
        </div>
        <div>
          <div className="flex items-center gap-2 mb-1.5">
            <span className="text-xs px-2 py-1 bg-green-100 text-green-800 rounded uppercase">{targetLang}</span>
            <span className="text-xs text-muted-foreground">번역</span>
          </div>
          <p className="leading-relaxed">{translatedText}</p>
        </div>
      </div>
    </div>
  );
}