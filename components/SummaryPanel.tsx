import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "./ui/card";
import { ScrollArea } from "./ui/scroll-area";
import { Badge } from "./ui/badge";
import { Clock, MessageSquare } from "lucide-react";

interface SummaryItem {
  id: string;
  timeRange: string;
  summary: string;
  keyPoints: string[];
}

interface SummaryPanelProps {
  currentTopic: string;
  summaries: SummaryItem[];
}

export function SummaryPanel({ currentTopic, summaries }: SummaryPanelProps) {
  return (
    <div className="h-full flex flex-col bg-white border-l">
      {/* 현재 주제 */}
      <div className="p-4 border-b bg-blue-50 shrink-0">
        <div className="flex items-start gap-2">
          <MessageSquare size={16} className="text-blue-600 mt-1 shrink-0" />
          <div>
            <h3 className="text-sm mb-1">현재 주제</h3>
            <p className="text-sm text-blue-800">{currentTopic}</p>
          </div>
        </div>
      </div>

      {/* 구간별 요약 */}
      {/* 구간별 요약 헤더 - 고정 */}
      <div className="p-4 border-b shrink-0">
        <div className="flex items-center gap-2">
          <Clock size={16} className="text-gray-600" />
          <h3 className="text-sm">구간별 요약</h3>
        </div>
      </div>

      {/* 구간별 요약 콘텐츠 - 스크롤 영역 */}
      <div className="flex-1 min-h-0">
        <ScrollArea className="h-full">
          <div className="p-4 space-y-4">
            {summaries.map((summary) => (
              <Card key={summary.id} className="shadow-sm">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Badge variant="outline" className="text-xs">
                      {summary.timeRange}
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <p className="text-sm text-gray-800 mb-3">
                    {summary.summary}
                  </p>
                  <div className="space-y-1">
                    <p className="text-xs text-gray-600">주요 포인트:</p>
                    <ul className="space-y-1">
                      {summary.keyPoints.map((point, index) => (
                        <li key={index} className="text-xs text-gray-700 flex items-start gap-1">
                          <span className="text-blue-500 mt-1">•</span>
                          <span>{point}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </ScrollArea>
      </div>
    </div>
  );
}