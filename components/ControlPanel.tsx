import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>c<PERSON> } from "lucide-react";
import { But<PERSON> } from "./ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import { Switch } from "./ui/switch";
import { Label } from "./ui/label";
import { QuestionHelper } from "./QuestionHelper";

interface ControlPanelProps {
  isRecording: boolean;
  onRecordingToggle: () => void;
  sourceLang: string;
  targetLang: string;
  onSourceLangChange: (lang: string) => void;
  onTargetLangChange: (lang: string) => void;
  isOnlineMode: boolean;
  onModeToggle: () => void;
  suggestedQuestions?: string[];
  isQuestionHelperVisible: boolean;
  onQuestionHelperToggle: () => void;
}

const languages = [
  { value: 'ko', label: '한국어' },
  { value: 'en', label: 'English' },
  { value: 'ja', label: '日本語' },
  { value: 'zh', label: '中文' },
  { value: 'auto', label: '자동 감지' }
];

export function ControlPanel({
  isRecording,
  onRecordingToggle,
  sourceLang,
  targetLang,
  onSourceLangChange,
  onTargetLangChange,
  isOnlineMode,
  onModeToggle,
  suggestedQuestions = [],
  isQuestionHelperVisible,
  onQuestionHelperToggle
}: ControlPanelProps) {
  const swapLanguages = () => {
    if (sourceLang !== 'auto' && targetLang !== 'auto') {
      onSourceLangChange(targetLang);
      onTargetLangChange(sourceLang);
    }
  };

  const handleQuestionSubmit = (question: string) => {
    // 질문 도우미는 참고용이므로 실제 전송 기능은 사용하지 않음
    console.log("Question for reference:", question);
  };

  return (
    <div className="border-t bg-gray-50 p-4 space-y-4 shrink-0">
      {/* 메인 컨트롤 그룹: 언어 선택 + 녹음 버튼 + 회의 모드 */}
      <div className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
        {/* 상단: 언어 선택과 녹음 컨트롤 */}
        <div className="flex items-center justify-between mb-4">
          {/* 좌측: Source 언어 선택 */}
          <div className="flex items-center gap-3">
            <Label className={isRecording ? "text-sm text-muted-foreground" : "text-sm"}>Source:</Label>
            <Select value={sourceLang} onValueChange={onSourceLangChange} disabled={isRecording}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {languages.map((lang) => (
                  <SelectItem key={lang.value} value={lang.value}>
                    {lang.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 중앙: 녹음 버튼과 언어 전환 버튼 */}
          <div className="flex items-center gap-4">
            {/* 녹음 버튼 */}
            <Button
              onClick={onRecordingToggle}
              variant={isRecording ? "destructive" : "default"}
              size="lg"
              className="relative shrink-0 h-14 w-14 rounded-full shadow-lg"
            >
              {isRecording ? <MicOff size={24} /> : <Mic size={24} />}
              {isRecording && (
                <span className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full animate-pulse"></span>
              )}
            </Button>

            {/* 언어 전환 버튼 */}
            <Button
              onClick={swapLanguages}
              variant="outline"
              size="sm"
              disabled={isRecording || sourceLang === 'auto' || targetLang === 'auto'}
              className="shrink-0 h-8 w-8 rounded-full p-0"
              title="언어 전환"
            >
              <RotateCcw size={14} />
            </Button>
          </div>

          {/* 우측: Target 언어 선택 */}
          <div className="flex items-center gap-3">
            <Label className={isRecording ? "text-sm text-muted-foreground" : "text-sm"}>Target:</Label>
            <Select value={targetLang} onValueChange={onTargetLangChange} disabled={isRecording}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {languages.filter(lang => lang.value !== 'auto').map((lang) => (
                  <SelectItem key={lang.value} value={lang.value}>
                    {lang.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* 하단: 회의 모드 토글 */}
        <div className="flex justify-center pt-3 border-t border-gray-100">
          <div className="flex items-center gap-3">
            <Label className="text-sm">회의 모드:</Label>
            <div className="flex items-center gap-2">
              <Label className="text-xs text-muted-foreground">오프라인</Label>
              <Switch checked={isOnlineMode} onCheckedChange={onModeToggle} />
              <Label className="text-xs text-muted-foreground">온라인</Label>
            </div>
          </div>
        </div>
      </div>

      {/* 질문 도우미 그룹 - 조건부 렌더링 */}
      {isQuestionHelperVisible && (
        <div className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
          <QuestionHelper
            onQuestionSubmit={handleQuestionSubmit}
            suggestedQuestions={suggestedQuestions}
            sourceLang={sourceLang}
            targetLang={targetLang}
          />
        </div>
      )}
    </div>
  );
}