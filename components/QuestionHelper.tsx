import { useState, useMemo, useRef } from "react";
import { Lightbulb, X, Languages } from "lucide-react";
import { Input } from "./ui/input";
import { Button } from "./ui/button";
import { Card, CardContent } from "./ui/card";
import { Badge } from "./ui/badge";

interface QuestionHelperProps {
  onQuestionSubmit: (question: string) => void;
  suggestedQuestions?: string[];
  sourceLang: string;
  targetLang: string;
}

// 언어 코드를 언어 이름으로 변환하는 함수
const getLanguageName = (langCode: string): string => {
  const languageMap: { [key: string]: string } = {
    'ko': '한국어',
    'en': 'English',
    'ja': '日本語',
    'zh': '中文',
    'auto': '자동 감지'
  };
  return languageMap[langCode] || langCode;
};

// 간단한 번역 시뮬레이션 함수 (실제로는 번역 API 사용)
const simulateTranslation = (text: string, sourceLang: string): string => {
  // sourceLang이 'auto'인 경우 'en'으로 처리 (실제로는 감지된 언어 사용)
  const actualSourceLang = sourceLang === 'auto' ? 'en' : sourceLang;
  
  const translations: { [key: string]: { [key: string]: string } } = {
    'en': {
      '분산 캐싱 구현 일정은 어떻게 되나요?': 'What is the implementation schedule for distributed caching?',
      '데이터베이스 샤딩 비용은 얼마나 들까요?': 'How much would database sharding cost?',
      '현재 시스템 모니터링 도구는 무엇을 사용하고 있나요?': 'What system monitoring tools are currently being used?',
      '성능': 'performance',
      '최적화': 'optimization',
      '데이터베이스': 'database',
      '캐시': 'cache',
      '모니터링': 'monitoring',
      '언제까지 완료될 예정인가요?': 'When is it expected to be completed?',
      '비용은 어느 정도 예상하시나요?': 'How much cost do you estimate?'
    },
    'ko': {
      'What is the implementation schedule for distributed caching?': '분산 캐싱 구현 일정은 어떻게 되나요?',
      'How much would database sharding cost?': '데이터베이스 샤딩 비용은 얼마나 들까요?',
      'What system monitoring tools are currently being used?': '현재 시스템 모니터링 도구는 무엇을 사용하고 있나요?',
      'performance': '성능',
      'optimization': '최적화',
      'database': '데이터베이스',
      'cache': '캐시',
      'monitoring': '모니터링',
      'When is it expected to be completed?': '언제까지 완료될 예정인가요?',
      'How much cost do you estimate?': '비용은 어느 정도 예상하시나요?'
    },
    'ja': {
      '분산 캐싱 구현 일정은 어떻게 되나요?': '分散キャッシングの実装スケジュールはどうなっていますか？',
      '데이터베이스 샤딩 비용은 얼마나 들까요?': 'データベースシャーディングのコストはどのくらいかかりますか？',
      '현재 시스템 모니터링 도구는 무엇을 사용하고 있나요?': '現在のシステム監視ツールは何を使用していますか？',
      '성능': 'パフォーマンス',
      '최적화': '最適化',
      '데이터베이스': 'データベース',
      '캐시': 'キャッシュ',
      '모니터링': 'モニタリング'
    },
    'zh': {
      '분산 캐싱 구현 일정은 어떻게 되나요?': '分布式缓存实施进度如何？',
      '데이터베이스 샤딩 비용은 얼마나 들까요?': '数据库分片的成本是多少？',
      '현재 시스템 모니터링 도구는 무엇을 사용하고 있나요?': '目前使用什么系统监控工具？',
      '성능': '性能',
      '최적화': '优化',
      '데이터베이스': '数据库',
      '캐시': '缓存',
      '모니터링': '监控'
    }
  };

  return translations[actualSourceLang]?.[text] || text;
};

export function QuestionHelper({ onQuestionSubmit, suggestedQuestions = [], sourceLang, targetLang }: QuestionHelperProps) {
  const [inputValue, setInputValue] = useState("");
  const [showTranslation, setShowTranslation] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  // 입력값을 기반으로 질문 제안 필터링
  const filteredSuggestions = useMemo(() => {
    if (!inputValue.trim()) return suggestedQuestions.slice(0, 3);
    
    const keywords = inputValue.toLowerCase().split(' ').filter(word => word.length > 0);
    
    return suggestedQuestions.filter(question =>
      keywords.some(keyword => 
        question.toLowerCase().includes(keyword)
      )
    ).slice(0, 3);
  }, [inputValue, suggestedQuestions]);

  // 번역과 원문 저장을 위한 상태
  const [savedTranslation, setSavedTranslation] = useState('');
  const [savedOriginalText, setSavedOriginalText] = useState('');
  
  // 번역된 질문 생성 (Target 언어에서 Source 언어로 번역)
  const translatedQuestion = useMemo(() => {
    if (!inputValue.trim()) return savedTranslation;
    const translation = simulateTranslation(inputValue, sourceLang);
    setSavedTranslation(translation);
    return translation;
  }, [inputValue, sourceLang, savedTranslation]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);
    // 자동 번역 비활성화 - 버튼 클릭시에만 번역 표시
  };

  const handleSuggestionClick = (suggestion: string) => {
    setInputValue(suggestion);
    // 자동 번역 비활성화
  };

  const handleTranslateClick = () => {
    if (inputValue.trim()) {
      setSavedOriginalText(inputValue); // 원문 저장
      setShowTranslation(true);
      setInputValue(""); // 번역 버튼 클릭 시 입력 필드 지우기
    }
  };

  const handleCloseTranslation = () => {
    setShowTranslation(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Cmd/Ctrl + A로 전체 선택 - 직접 구현
    if ((e.metaKey || e.ctrlKey) && e.key === 'a') {
      e.preventDefault();
      if (inputRef.current) {
        inputRef.current.select();
      }
      return;
    }
    
    if (e.key === 'Enter') {
      e.preventDefault();
      if (showTranslation) {
        // 번역 결과가 표시된 상태에서 Enter 키를 누르면 팝업 닫기
        handleCloseTranslation();
      } else if (inputValue.trim()) {
        // 입력값이 있을 때만 번역 시작
        handleTranslateClick();
      }
    }
  };

  // 실제 번역 대상 언어 결정 (auto인 경우 처리)
  const actualSourceLang = sourceLang === 'auto' ? 'en' : sourceLang;

  return (
    <div className="relative space-y-3">
      {/* 질문 도우미 헤더 */}
      <div className="flex items-center gap-2">
        <Lightbulb size={16} className="text-amber-500" />
        <h3 className="text-sm">질문 도우미</h3>
        <Badge variant="outline" className="text-xs bg-gray-100 text-gray-600">
          {getLanguageName(targetLang)} → {getLanguageName(actualSourceLang)}
        </Badge>
      </div>

      {/* 질문 입력 필드 */}
      <div className="space-y-2">
        <div className="flex gap-2">
          <Input
            ref={inputRef}
            value={inputValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            placeholder={`질문을 ${getLanguageName(targetLang)}로 입력하세요...`}
            className="text-sm flex-1"
          />
          <Button
            onClick={handleTranslateClick}
            disabled={!inputValue.trim()}
            variant="outline"
            size="sm"
            className="shrink-0"
          >
            <Languages size={14} className="mr-1" />
            번역
          </Button>
        </div>
        <p className="text-xs text-gray-500">
          {getLanguageName(targetLang)}로 입력하면 {getLanguageName(actualSourceLang)}로 번역해드립니다
        </p>
      </div>

      {/* 추천 질문들 */}
      {filteredSuggestions.length > 0 && (
        <div className="space-y-2">
          <p className="text-xs text-gray-600">추천 질문:</p>
          <div className="space-y-1">
            {filteredSuggestions.map((question, index) => (
              <button
                key={index}
                onClick={() => handleSuggestionClick(question)}
                className="w-full text-left p-2 text-xs bg-gray-50 hover:bg-gray-100 rounded border text-gray-700 transition-colors"
              >
                {question}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* 번역된 질문 오버레이 */}
      {showTranslation && (
        <div 
          className="absolute inset-0 z-10 bg-white/95 backdrop-blur-sm rounded-lg border shadow-lg animate-in fade-in-0 duration-200"
          tabIndex={0}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === 'Escape') {
              e.preventDefault();
              handleCloseTranslation();
            }
          }}
        >
          <Card className="h-full bg-green-50 border-green-200 shadow-sm">
            <CardContent className="p-4 h-full flex flex-col">
              {/* 헤더 */}
              <div className="flex items-center justify-between mb-3">
                <Badge variant="outline" className="text-xs bg-green-100 text-green-700 border-green-300">
                  {getLanguageName(actualSourceLang)}로 번역됨
                </Badge>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleCloseTranslation}
                  className="h-6 w-6 p-0 hover:bg-green-200 text-green-600"
                >
                  <X size={14} />
                </Button>
              </div>
              
              {/* 번역 내용 */}
              <div className="flex-1 flex flex-col justify-center space-y-3">
                {/* 원문 (Target 언어) */}
                <div className="text-center">
                  <p className="text-xs text-green-500 opacity-70 mb-1">
                    원문 ({getLanguageName(targetLang)})
                  </p>
                  <p className="text-xs text-green-600 opacity-80 leading-relaxed whitespace-normal break-words">
                    {savedOriginalText}
                  </p>
                </div>
                
                {/* 구분선 */}
                <div className="flex items-center gap-2 px-4">
                  <div className="flex-1 h-px bg-green-200"></div>
                  <Languages size={12} className="text-green-400" />
                  <div className="flex-1 h-px bg-green-200"></div>
                </div>
                
                {/* 번역문 (Source 언어) */}
                <div className="text-center">
                  <p className="text-xs text-green-500 opacity-70 mb-1">
                    번역 ({getLanguageName(actualSourceLang)})
                  </p>
                  <p className="text-sm text-green-800 leading-relaxed whitespace-normal break-words">
                    {translatedQuestion}
                  </p>
                </div>
              </div>
              
              {/* 사용법 안내 */}
              <div className="text-center mt-3">
                <p className="text-xs text-green-600 opacity-75">
                  Enter 또는 ESC 키를 눌러 닫기
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}